import { NonNullableValues } from '../../utils/TypeUtils';

import { AppState, StepInformation } from '../../domain/AppState';
import { FECError } from '../../domain/FECError';
import { FECBookingError, BookingResponse } from '../../domain/Booking';
import { PriceSummary } from '../../domain/PriceSummary';
import { Rate } from '../../domain/Rate';
import { SelectedExtra, UserData } from '../../domain/UserInput';
import { CreditCardType } from '../../domain/Price';
import { RentalParameters } from '../../domain/Rental';
import { OptionalInsurance } from '../../domain/Insurances';
import { AutocompleteSuggestion } from '../../domain/Autocomplete';
import { SearchParameters } from '../../domain/SearchParameters';
import { Filter } from '../../domain/Filter';
import { StationInteractionType } from '../../domain/Station';
import { OfferDetailsTab } from '../../domain/OfferDetailsTab';

export type AnalyticsStep = 1 | 2 | 3;

export type BookingInfo = Pick<
    NonNullableValues<AppState>,
    'rate' | 'priceSummary' | 'userSelection' | 'userData' | 'sourceLandingPage'
>;

export interface Analytics {
    // Common
    onTermsAndConditionsScroll(isScrolledToEnd: boolean, rateId?: string): void;
    onTermsAndConditionsSearchTerm(term: string): void;
    onTermsAndConditionsSearchCycle(term: string): void;

    // Results view
    onAutoCompleteSuggestionSelected(suggestion: AutocompleteSuggestion): void;
    onGoToOffer(search: SearchParameters | null, value: number, isHappyCar: boolean, isFirstStep?: boolean): void;
    onPickUpLocationChange(): void;
    onDropOffLocationChange(): void;
    onPickUpDateChange(): void;
    onDropOffDateChange(): void;
    onTimeChange(): void;
    onHelpMenuOpened(): void;
    onFirstPageLoadEvent(): void;
    onBannerCloseEvent(): void;
    onFirstDataLoadEvent(search: SearchParameters | null): void;
    onPackagesBannerSelected(packageType: string): void;
    onPaginationClick(currentPage: number, nextPage: number): void;
    onStationSelected(label: StationInteractionType, isCluster: boolean, isOneWay: boolean): void;
    onStationRemoved(label: StationInteractionType): void;
    onMapAirportSelected(label: StationInteractionType): void;
    onMapNavigationStepChange(label: StationInteractionType, step: 1 | 2, isOneWay: boolean): void;
    onMapStationTypeChange(label: StationInteractionType): void;
    onTermsAndConditionsToggle(isOpen: boolean): void;
    onTermsAndConditionsTabOpen(tab: OfferDetailsTab): void;
    onLoadBufferedOffers(location: string): void;

    onFilterSelected(filter: Filter): void;

    // Booking form
    /**
     * Load
     */
    onLoad(rate: Rate, priceSummary: PriceSummary): void;

    /**
     * Insurance
     */
    onInsuranceError(): void;
    onInsuranceSelected(insuranceId?: string): void;

    /**
     * Coupon
     */
    onCouponChange(couponOrResult: string | boolean): void;

    /**
     * Extras
     */
    onMultipleExtrasSelected(): void;
    onExtrasChange(extra: SelectedExtra): void;

    /**
     * On data change
     */
    onUserDataChange(rate: Rate, priceSummary: PriceSummary, field: keyof UserData): void;
    onCreditCardNumberInvalidated(paymentMethods: CreditCardType[], creditCard: string | null): void;

    /**
     * On booking
     */
    onBeforeRateBooking(): void;
    onBookingSuccess(
        info: BookingInfo,
        optionalInsurance: OptionalInsurance | null,
        bookingResponse: BookingResponse,
    ): void;
    onBookingChallenged(): void;
    onBookingError(error: FECError | FECBookingError): void;

    /**
     * Errors
     */
    onRateIsUnavailable(): void;

    /**
     * Interactions
     */
    onBackToResultView(): void;
    onSupplierTabsToggle(previousTab: StepInformation | null, tab: StepInformation | null): void;
    onAddressDetailsToggle(isExpanding: boolean): void;
    onStationTimeOpenAttempt(interactionType: keyof RentalParameters): void;
    usedServiceCode(): void;
}
