import { AppState } from '../../../domain/AppState';
import { AdditionalData, Filters, PackageFilter } from '../../../domain/Filter';
import { StationInteractionType } from '../../../domain/Station';

import { INITIAL_PAGE } from '../../InitialState';

import { Usecase } from '../Usecase';
import LoadDataUsecase from '../LoadDataUsecase';

interface Props {
    readonly filters: Filters[];
    readonly filterName?: string;
    readonly groupName?: string;
    readonly selected: boolean;
}

export class FiltersUsecase extends Usecase {
    protected update({ currentPage, ...state }: Partial<AppState>): void {
        this.setState({
            ...state,
            /**
             * Whatever was selected previously can be dropped
             *
             * It was either already partially in effect,
             * Or the user wishes to overwrite it
             */
            forceSelection: null,
            currentPage: currentPage || INITIAL_PAGE,
        });

        const loadDataUsecase = this.getUsecase(LoadDataUsecase);
        loadDataUsecase.requestData();
    }

    protected getUpdatedFilters = ({ filters, filterName, groupName, selected }: Props): Filters[] => {
        return filters.map((filter) => ({
            ...filter,
            itemsList: filter.itemsList.map((item) =>
                filterName
                    ? item.translationKey === filterName
                        ? { ...item, selected, subItems: item.subItems?.map((item) => ({ ...item, selected })) }
                        : {
                              ...item,
                              subItems: item.subItems?.map((item) =>
                                  item.translationKey === filterName ? { ...item, selected } : item,
                              ),
                          }
                    : !groupName || item.type === groupName
                    ? {
                          ...item,
                          selected,
                          subItems: item.subItems?.map((item) => ({ ...item, selected })),
                      }
                    : {
                          ...item,
                          subItems: item.subItems.map((item) =>
                              item.translationKey === groupName ? { ...item, selected } : item,
                          ),
                      },
            ),
            subItems: this.getUpdatedFilters({ filters: filter.subItems, filterName, groupName, selected }),
        }));
    };

    protected removeSelectedFilters(filters: Filters[], values?: AdditionalData[]): Filters[] {
        if (values) {
            return filters.map((filter) => ({
                ...filter,
                itemsList: filter.itemsList.map((item) => ({
                    ...item,
                    selected:
                        //If all values from filter are in package values then deselect filter
                        item.values.some((i) => !values.some((v) => v.value === i.value && v.key === i.key)) &&
                        item.selected,
                })),
                subItems: filter.subItems.map((filter) => ({
                    ...filter,
                    itemsList: filter.itemsList.map((item) => ({
                        ...item,
                        selected:
                            //If all values from filter are in package values then deselect filter
                            item.values.some((i) => !values.some((v) => v.value === i.value && v.key === i.key)) &&
                            item.selected,
                    })),
                })),
            }));
        }
        return filters;
    }

    protected removePackageFilters(packages: PackageFilter[], values?: AdditionalData[]): PackageFilter[] {
        return values
            ? packages.map((p) => ({
                  ...p,
                  selected:
                      //If all values from filter are in package values then deselect package
                      values.some((i) => !values.some((v) => v.value === i.value && v.key === i.key)) && p.selected,
              }))
            : packages;
    }

    updateFilter = (filterName: string, selected: boolean, values?: AdditionalData[]) => {
        const { filters, packages } = this.getState();
        this.update({
            packages: selected ? packages : this.removePackageFilters(packages, values),
            filters: this.getUpdatedFilters({ filters, selected, filterName }),
        });
    };

    clearFilters(groupName?: string): void {
        const { filters, packages, stations } = this.getState();
        this.update({
            filters: this.getUpdatedFilters({ filters, groupName, selected: false }),
            packages: groupName ? packages : packages.map((p) => ({ ...p, selected: false })),
            stations: groupName ? stations : { ...stations, selectedPickUpIds: [], selectedDropOffIds: [] },
        });
    }

    clearStations(stationsType: StationInteractionType): void {
        const { stations } = this.getState();
        const type = stationsType === 'pickUp' ? 'selectedPickUpIds' : 'selectedDropOffIds';

        this.update({
            stations: { ...stations, [type]: [] },
        });
    }

    updateCurrentPage(currentPage: number): void {
        this.update({ currentPage });
    }

    updatePackageFilter(filterName: string, selected: boolean): void {
        const { packages, filters } = this.getState();
        const selectedPackage = packages.find((p) => p.translationKey === filterName);
        this.update({
            filters: selected ? filters : this.removeSelectedFilters(filters, selectedPackage?.values),
            packages: packages.map((p) => (p.translationKey === filterName ? { ...p, selected } : p)),
        });
    }

    updateStationFilters(stationsIds: string[], stationsType: StationInteractionType, selected: boolean): void {
        const { stations } = this.getState();

        const type = stationsType === 'pickUp' ? 'selectedPickUpIds' : 'selectedDropOffIds';

        let ids = stations[type];

        if (selected) {
            ids.push(...stationsIds);
        } else {
            ids = ids.filter((id) => !stationsIds.some((sid) => sid === id));
        }

        this.update({ stations: { ...stations, [type]: ids.filter((x, i, a) => a.indexOf(x) == i) } });
    }
}
