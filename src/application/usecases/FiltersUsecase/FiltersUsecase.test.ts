import { getSelectedCount } from '../../../domain/Filter';

import { filtersResponse } from '../../../infrastructure/services/mappers/Rates/FiltersResponse';

import FiltersUsecase from '.';
import LoadDataUsecase from '../LoadDataUsecase';

import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';

test('Test updateCurrentPage function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const filtersUsecase = new FiltersUsecase(...injections);

    expect(store.getState().currentPage).toBe(1);

    filtersUsecase.updateCurrentPage(5);

    expect(store.getState().currentPage).toBe(5);

    expect(requestDataMock).toBeCalledTimes(1);
});

test('Test updateFilter function', () => {
    const store = createInitialTestState({
        filters: filtersResponse,
    });
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const filtersUsecase = new FiltersUsecase(...injections);

    expect(store.getState().filters[0].subItems[0].itemsList[0].selected).toBe(false);
    filtersUsecase.updateFilter('LABEL_FILTER_INSURANCE_CDW_NO_DEDUCTIBLE', true);
    expect(store.getState().filters[0].subItems[0].itemsList[0].selected).toBe(true);

    expect(store.getState().filters[2].itemsList[0].subItems[0].selected).toBe(false);
    filtersUsecase.updateFilter('BER', true);
    expect(store.getState().filters[2].itemsList[0].subItems[0].selected).toBe(true);

    expect(requestDataMock).toBeCalledTimes(2);
});

test('Test clearFilters function', () => {
    const store = createInitialTestState({
        filters: filtersResponse,
    });
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const filtersUsecase = new FiltersUsecase(...injections);

    expect(getSelectedCount(store.getState())).toBe(5);

    filtersUsecase.clearFilters();
    expect(getSelectedCount(store.getState())).toBe(0);
    expect(requestDataMock).toBeCalledTimes(1);
});

test('Test clearFilters function for carTypes', () => {
    const store = createInitialTestState({
        filters: filtersResponse,
    });
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const filtersUsecase = new FiltersUsecase(...injections);

    expect(getSelectedCount(store.getState())).toBe(5);
    expect(store.getState().filters[3].itemsList.filter((item) => item.selected).length).toBe(2);

    filtersUsecase.clearFilters('carTypes');

    expect(getSelectedCount(store.getState())).toBe(3);
    expect(store.getState().filters[3].itemsList.filter((item) => item.selected).length).toBe(0);
    expect(requestDataMock).toBeCalledTimes(1);
});
