import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';
import { Parameters } from '../../../domain/Parameters';
import { Platform } from '../../../domain/Platform';
import { Salutation } from '../../../domain/Salutation';
import { LoadingStatus } from '../../../domain/AppState';

import { InitialBookingInformation, OptionalInsurancesInformation } from '../../services';

import { NonNullableValues } from '../../../utils/TypeUtils';

import LoadRateUsecase from '.';

import {
    mockedCountries,
    mockedExcellentBookingInformation,
    mockedGetOptionalInsurance,
    mockedGoodBookingInformation,
} from '../../../../test/services/MockedBackend';
import { createInitialTestState, createUsecaseInjections, delay } from '../../../../test/TestUtils';
import { CreateProvisionalBookingParameters } from '../../../domain/Booking';

test('Test load function without parameters expect error', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const createLoadRateUsecase = new LoadRateUsecase(...injections);

    const [, { loggerService }] = injections;

    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;
    const parameters = { rateId: '', rateSearchKey: '' };
    createLoadRateUsecase.load(parameters);

    const error = wrapError(
        new FECError(FECErrorType.BAD_PARAMETERS, 'Parameters could not be used to load rate', {
            ...parameters,
        }),
        FECErrorType.RATE_LOAD,
    );
    expect(store.getState().error).toStrictEqual(error);

    expect(loggerErrorMock).toBeCalledWith(error);
});

test('Test load function with invalid BookingInformation expect error', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const createLoadRateUsecase = new LoadRateUsecase(...injections);

    const [, { backend, loggerService }] = injections;

    const bookingError = new FECError(FECErrorType.RATE_UNAVAILABLE);
    const fetchBookingInformationMock = jest
        .fn<Promise<InitialBookingInformation>, [NonNullableValues<Parameters>]>()
        .mockRejectedValue(bookingError);
    backend.fetchBookingInformation = fetchBookingInformationMock;
    const fetchOptionalInsuranceMock = jest
        .fn<Promise<OptionalInsurancesInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedGetOptionalInsurance));
    backend.fetchOptionalInsurance = fetchOptionalInsuranceMock;

    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    createLoadRateUsecase.load({ rateId: 'invalid', rateSearchKey: 'key' });
    await delay(0);

    const error = wrapError(bookingError, FECErrorType.RATE_LOAD);
    expect(store.getState().error).toStrictEqual(error);

    expect(loggerErrorMock).toBeCalledWith(error);
    expect(fetchBookingInformationMock).toBeCalledTimes(1);
    expect(fetchOptionalInsuranceMock).toBeCalledTimes(0);
});

test('Test load function with invalid verification', async () => {
    const store = createInitialTestState({}, { defaultSalutation: Salutation.FEMALE, platform: Platform.US });
    const injections = createUsecaseInjections(store);
    const createLoadRateUsecase = new LoadRateUsecase(...injections);

    const [, { analytics, backend, loggerService }] = injections;
    const verifyError = new FECError(FECErrorType.RATE_NOT_VERIFIED);
    const fetchBookingInformationMock = jest
        .fn<Promise<InitialBookingInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedExcellentBookingInformation));
    backend.fetchBookingInformation = fetchBookingInformationMock;
    const fetchOptionalInsuranceMock = jest
        .fn<Promise<OptionalInsurancesInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedGetOptionalInsurance));
    backend.fetchOptionalInsurance = fetchOptionalInsuranceMock;
    const verifyOfferMock = jest.fn<Promise<void>, [NonNullableValues<Parameters>]>().mockRejectedValue(verifyError);
    backend.verifyOffer = verifyOfferMock;

    const onLoadMock = jest.fn<void, []>();
    analytics.onLoad = onLoadMock;

    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    createLoadRateUsecase.load({ rateId: 'id', rateSearchKey: 'key' });
    await delay(0);

    const error = wrapError(verifyError, FECErrorType.RATE_LOAD);
    expect(store.getState().error).toStrictEqual(error);
    expect(store.getState().loading).toStrictEqual(LoadingStatus.LOADED_SUCCESS);

    expect(onLoadMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledWith(error);
    expect(fetchBookingInformationMock).toBeCalledTimes(1);
    expect(verifyOfferMock).toBeCalledTimes(1);
    expect(fetchOptionalInsuranceMock).toBeCalledTimes(0);
});

test('Test load function with success BookingInformation with coupon code, mandatory FlightNumber, default Salutation and selected Country', async () => {
    const store = createInitialTestState({}, { defaultSalutation: Salutation.FEMALE, platform: Platform.US });
    const injections = createUsecaseInjections(store);
    const createLoadRateUsecase = new LoadRateUsecase(...injections);

    const [, { analytics, backend }] = injections;

    const fetchBookingInformationMock = jest
        .fn<Promise<InitialBookingInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedExcellentBookingInformation));
    backend.fetchBookingInformation = fetchBookingInformationMock;
    const fetchOptionalInsuranceMock = jest
        .fn<Promise<OptionalInsurancesInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedGetOptionalInsurance));
    backend.fetchOptionalInsurance = fetchOptionalInsuranceMock;
    const verifyOfferMock = jest.fn<Promise<void>, [NonNullableValues<Parameters>]>();
    backend.verifyOffer = verifyOfferMock;
    const createProvisionalBookingMock = jest.fn<
        Promise<number | null>,
        [NonNullableValues<CreateProvisionalBookingParameters>]
    >();
    backend.createProvisionalBooking = createProvisionalBookingMock;

    const onLoadMock = jest.fn<void, []>();
    analytics.onLoad = onLoadMock;

    createLoadRateUsecase.load({ rateId: 'id', rateSearchKey: 'key' });
    await delay(0);

    expect(store.getState().error).toBe(null);
    expect(store.getState().userData.salutation.value).toBe(Salutation.FEMALE);
    expect(store.getState().userData.coupon.value).toBe(mockedExcellentBookingInformation.couponCode);
    expect(store.getState().userData.flightNumber.required).toBe(true);
    expect(store.getState().userData.country.value).toStrictEqual(mockedCountries[0]);
    expect(store.getState().userSelection.selectedInsurance).toStrictEqual(
        mockedExcellentBookingInformation.selectedInsurance,
    );

    expect(onLoadMock).toBeCalledTimes(1);
    expect(fetchBookingInformationMock).toBeCalledTimes(1);
    expect(fetchOptionalInsuranceMock).toBeCalledTimes(1);
    expect(verifyOfferMock).toBeCalledTimes(1);
    expect(createProvisionalBookingMock).toBeCalledTimes(1);
});

test('Test load function with success BookingInformation with credit card is not required and selected insurance', async () => {
    const store = createInitialTestState({}, { platform: Platform.CH });
    const injections = createUsecaseInjections(store);
    const createLoadRateUsecase = new LoadRateUsecase(...injections);

    const [, { analytics, backend }] = injections;

    const fetchBookingInformationMock = jest
        .fn<Promise<InitialBookingInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedGoodBookingInformation));
    backend.fetchBookingInformation = fetchBookingInformationMock;
    const fetchOptionalInsuranceMock = jest
        .fn<Promise<OptionalInsurancesInformation>, [NonNullableValues<Parameters>]>()
        .mockReturnValue(Promise.resolve(mockedGetOptionalInsurance));
    backend.fetchOptionalInsurance = fetchOptionalInsuranceMock;
    const verifyOfferMock = jest.fn<Promise<void>, [NonNullableValues<Parameters>]>();
    backend.verifyOffer = verifyOfferMock;
    const createProvisionalBookingMock = jest.fn<
        Promise<number | null>,
        [NonNullableValues<CreateProvisionalBookingParameters>]
    >();
    backend.createProvisionalBooking = createProvisionalBookingMock;

    const onLoadMock = jest.fn<void, []>();
    analytics.onLoad = onLoadMock;

    createLoadRateUsecase.load({ rateId: 'id', rateSearchKey: 'key' });
    await delay(0);

    expect(store.getState().error).toBe(null);
    expect(store.getState().userData.coupon.value).toBe(null);
    expect(store.getState().userData.flightNumber.required).toBe(false);
    expect(store.getState().userData.creditCardNumber.required).toBe(false);
    expect(store.getState().userSelection.selectedInsurance).toStrictEqual(
        mockedGoodBookingInformation.selectedInsurance,
    );

    expect(onLoadMock).toBeCalledTimes(1);
    expect(fetchBookingInformationMock).toBeCalledTimes(1);
    expect(fetchOptionalInsuranceMock).toBeCalledTimes(1);
    expect(verifyOfferMock).toBeCalledTimes(1);
    expect(createProvisionalBookingMock).toBeCalledTimes(1);
});
