import { differenceInHours } from 'date-fns';

import { DynamicConfiguration } from '../../../domain/Configuration';
import { Parameters } from '../../../domain/Parameters';
import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';
import { ValueState } from '../../../domain/UserInput';
import { isCreditCardRequired } from '../../../domain/BookingInformation';
import { BookingStatus, generateCreditCardRequirements, LoadingStatus, Page } from '../../../domain/AppState';

import { createInitialUserField, initialState } from '../../InitialState';

import { Usecase } from '../Usecase';

import { getCountries } from '../../../utils/CountryList';

import { mapOptionalInsurances } from '../../../infrastructure/services/mappers/Insurances';

export class LoadRateUsecase extends Usecase {
    /**
     * Handles async loader and sets the error
     */
    private async wrapErrors(loader: () => Promise<void> | void): Promise<void> {
        try {
            await loader();
        } catch (e) {
            const error = wrapError(e, FECErrorType.RATE_LOAD);
            this.loggerService.error(error);
            this.setState({ error });
        }
    }

    load(parameters: Parameters): void {
        const { configuration, countries, userData, rateId } = this.getState();

        if (rateId === parameters.rateId) {
            this.setState({
                page: Page.BOOKING_FORM,
                error: null,

                showHelp: false,
                stepInformation: null,
                bookingResponse: null,
                bookingStatus: BookingStatus.INITIAL,
                loading: LoadingStatus.VERIFIED,
                secureCardTransactionId: null,
                userData: {
                    ...userData,
                    securedCreditCardNumber: createInitialUserField({ value: null }),
                    securedCreditCardSecurityCode: createInitialUserField({ value: null }),
                },

                offer: null,
                detailsTab: null,
            });
        } else {
            /**
             * Reset data and set initial
             */
            this.setState({
                ...parameters,
                page: Page.BOOKING_FORM,
                error: null,

                rate: null,
                priceSummary: null,
                userSelection: initialState.userSelection,
                showHelp: false,
                stepInformation: null,
                optionalInsurance: null,
                loading: LoadingStatus.LOADING,
                bookingStatus: BookingStatus.INITIAL,
                bookingResponse: null,
                secureCardTransactionId: null,
                userData: {
                    ...userData,
                    securedCreditCardNumber: createInitialUserField({ value: null }),
                    securedCreditCardSecurityCode: createInitialUserField({ value: null }),
                },

                offer: null,
                detailsTab: null,
            });

            /**
             * Load countries
             */
            if (!countries.length) {
                this.setState({ countries: getCountries(configuration.platform) });
            }

            void this.wrapErrors(() => {
                if (!parameters.rateSearchKey || !parameters.rateId) {
                    throw new FECError(FECErrorType.BAD_PARAMETERS, 'Parameters could not be used to load rate');
                }

                /**
                 * Setup booking info
                 */
                void this.wrapErrors(async () => {
                    const { information, selectedInsurance, couponCode } = await this.backend.fetchBookingInformation(
                        parameters,
                    );

                    const { userData, configuration, userSelection, countries } = this.getState();
                    const { isFlightNumberMandatory } = information.rate;

                    const isCardRequired = isCreditCardRequired(information.rate, information.priceSummary);
                    const { dynamicConfiguration } = this.getState();

                    const { pickUpTime, createdAt: searchTime } = information;
                    /*
                     * If the difference between search time and pickup time is less than 24 hours
                     * hide the free cancellation label
                     */
                    const isLessThanADayPriorPickup =
                        pickUpTime && searchTime ? differenceInHours(pickUpTime, searchTime) < 24 : true;

                    this.setState({
                        ...information,
                        dynamicConfiguration: {
                            ...(information.dynamicConfiguration as DynamicConfiguration),
                            ...dynamicConfiguration,
                        },
                        loading: LoadingStatus.LOADED_SUCCESS,
                        userData: {
                            ...generateCreditCardRequirements(userData, information.rate, isCardRequired),
                            salutation: {
                                ...userData.salutation,
                                value: configuration.defaultSalutation,
                            },
                            flightNumber: { ...userData.flightNumber, required: isFlightNumberMandatory },
                            country: {
                                ...userData.country,
                                value: countries.find((c) => c.countryCode === configuration.platform) || null,
                            },
                            coupon: {
                                ...userData.coupon,
                                ...(couponCode ? { value: couponCode, state: ValueState.VALID } : {}),
                            },
                        },
                        showAttentionMessage: configuration.showAttentionMessage,
                        userSelection: { ...userSelection, selectedInsurance },

                        isLessThanADayPriorPickup,
                    });

                    /**
                     * Signal loading
                     */
                    this.analytics.onLoad(information.rate, information.priceSummary);

                    /**
                     *Call verify endpoint
                     */
                    await this.backend.verifyOffer(parameters);
                    this.setState({ loading: LoadingStatus.VERIFIED });

                    /**
                     *Call create provisional booking endpoint
                     */
                    await this.backend.createProvisionalBooking({
                        rateId: parameters.rateId,
                        rateSearchKey: parameters.rateSearchKey,
                        countryCode: configuration.platform,
                    });

                    /**
                     *Call fetchOptionalInsurance endpoint
                     */
                    const { optionalInsurances, optionalInsuranceConfiguration } =
                        await this.backend.fetchOptionalInsurance(parameters);
                    const optionalInsurance = mapOptionalInsurances({
                        optionalInsurances,
                        driverAge: information.rate.driverAge,
                        rental: information.rate.rental,
                        configuration: optionalInsuranceConfiguration,
                    });
                    this.setState({ optionalInsurance });
                });
            });
        }
    }
}
