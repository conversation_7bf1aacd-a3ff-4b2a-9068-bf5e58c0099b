import { FECErrorType, wrapError } from '../../../domain/FECError';
import { LoadingStatus as RVLoadingStatus } from '../../../domain/LoadingStatus';
import { Page } from '../../../domain/AppState';

import { Backend, RVUrlParameters } from '../../services';

import BannersUsecase from '../BannersUsecase';
import LoadDataUsecase from '../LoadDataUsecase';
import { Usecase } from '../Usecase';

import { mapFiltersRequest } from '../../../infrastructure/services/mappers/Filters/Request';

// TODO: Think how can (do we want) we use this use case for BookingForm case when RV and BF pages are implemented
export class URLUsecase extends Usecase {
    protected handleError = (e: unknown, erroType: FECErrorType): void => {
        const error = wrapError(e, erroType);

        this.setState({ loadingRV: RVLoadingStatus.LOADED_ERROR, error });
        this.loggerService.error(error);
    };

    protected makeNewSearch = async (backend: Backend, rateSearchKey: string): Promise<string> => {
        const search = await backend.fetchSearch(rateSearchKey);
        return await backend.startSearch(search);
    };

    loadDataFromUrl = async (): Promise<void> => {
        let input: RVUrlParameters;

        try {
            input = this.urlSerializer.deserialize(...this.browserHistoryService.getUrlParameters());
        } catch (error) {
            this.handleError(error, FECErrorType.UNEXPECTED_MAPPING_SCENARIO);
            return;
        }

        if (input.makeNewSearch) {
            try {
                input = { ...input, rateSearchKey: await this.makeNewSearch(this.backend, input.rateSearchKey) };
            } catch (error) {
                this.handleError(error, FECErrorType.UNEXPECTED_SEARCH_START);
                return;
            }
        }

        const { rateSearchKey, filters: forceSelection, currentPage } = input;

        //If rateSearchKey is changed or added for first time then set logger id
        if (rateSearchKey && rateSearchKey !== this.getState().rateSearchKey) {
            this.loggerService.setRateSearchKey(rateSearchKey);
        }

        this.setState({ rateSearchKey, forceSelection, currentPage, page: Page.RESULT_VIEW });
        this.getUsecase(BannersUsecase).loadBanners();

        const loadDataUsecase = this.getUsecase(LoadDataUsecase);
        loadDataUsecase.requestData(true);
    };

    offLoadToUrl = (): void => {
        const { rateSearchKey, currentPage, filters, packages } = this.getState();

        if (rateSearchKey) {
            const input: RVUrlParameters = {
                rateSearchKey,
                currentPage,
                filters: mapFiltersRequest(filters, null, packages),
                makeNewSearch: false,
            };
            this.browserHistoryService.push(...this.urlSerializer.serialize(input));
        }
    };
}
