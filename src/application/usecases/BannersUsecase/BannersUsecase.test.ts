import { AdBannerType, PersistedBannerConfig } from '../../../domain/AdBanner';
import { FECError, FECErrorType } from '../../../domain/FECError';

import { initialState } from '../../InitialState';

import BannersUsecase from '.';

import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';

test('Test closeBanner function without rateSearchKey', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence }] = injections;

    const persistMock = jest.fn<boolean, [PersistedBannerConfig]>();
    bannersPersistence.persist = persistMock;

    expect(store.getState().banners).toStrictEqual(initialState.banners);

    bannersUsecase.closeBanner('adsBanner');

    expect(store.getState().banners).toStrictEqual(initialState.banners);
    expect(persistMock).toBeCalledTimes(0);
});

test('Test closeBanner function', () => {
    const store = createInitialTestState({ rateSearchKey: 'rateSearchKey' });
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence }] = injections;

    const persistMock = jest.fn<boolean, [PersistedBannerConfig]>();
    bannersPersistence.persist = persistMock;

    expect(store.getState().banners).toStrictEqual(initialState.banners);

    bannersUsecase.closeBanner('adsBanner');
    expect(store.getState().banners).toStrictEqual({ ...initialState.banners, adsBanner: false });
    expect(persistMock).toBeCalledTimes(1);

    bannersUsecase.closeBanner('premiumPackageFilterBanner');
    expect(store.getState().banners).toStrictEqual({
        ...initialState.banners,
        adsBanner: false,
        premiumPackageFilterBanner: false,
    });
    expect(persistMock).toBeCalledTimes(2);

    bannersUsecase.closeBanner('shortCarSupply');
    expect(store.getState().banners).toStrictEqual({
        ...initialState.banners,
        adsBanner: false,
        premiumPackageFilterBanner: false,
        shortCarSupply: false,
    });
    expect(persistMock).toBeCalledTimes(3);

    bannersUsecase.closeBanner('mapOffersUpdated');
    expect(store.getState().banners).toStrictEqual({
        ...initialState.banners,
        adsBanner: false,
        premiumPackageFilterBanner: false,
        shortCarSupply: false,
        mapOffersUpdated: false,
    });

    bannersUsecase.closeBanner('covidBanner');
    expect(store.getState().banners).toStrictEqual({
        adsBanner: false,
        premiumPackageFilterBanner: false,
        shortCarSupply: false,
        mapOffersUpdated: false,
        covidBanner: false,
    });

    expect(persistMock).toBeCalledTimes(5);
});

test('Test loadBanners function without rateSearchKey', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence }] = injections;

    const findAllMock = jest.fn<PersistedBannerConfig[], [string]>();
    bannersPersistence.findAll = findAllMock;

    bannersUsecase.loadBanners();

    expect(store.getState().banners).toStrictEqual(initialState.banners);
    expect(store.getState().adBannerType).toBe(null);

    expect(findAllMock).toBeCalledTimes(0);
});

test('Test loadBanners function with error from bannersPersistence', () => {
    const store = createInitialTestState({ rateSearchKey: 'testWithGraph' });
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence, loggerService }] = injections;

    const error = new FECError(FECErrorType.UNEXPECTED_PERSISTED_DATA, 'Could not load banner persisted configuration');

    const findAllMock = jest.fn<PersistedBannerConfig[], [string]>(() => {
        throw error;
    });
    bannersPersistence.findAll = findAllMock;
    const errorMock = jest.fn<void, [FECError]>();
    loggerService.error = errorMock;

    bannersUsecase.loadBanners();

    expect(store.getState().banners).toStrictEqual(initialState.banners);
    expect(store.getState().adBannerType).toBe(AdBannerType.WITH_GRAPH);

    expect(findAllMock).toBeCalledTimes(1);
    expect(errorMock).toBeCalledTimes(1);
    expect(errorMock).toBeCalledWith(error);
});

test('Test loadBanners function without saved banners', () => {
    const store = createInitialTestState({ rateSearchKey: 'testWithGraph' });
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence }] = injections;

    const findAllMock = jest.fn<PersistedBannerConfig[], [string]>().mockReturnValue([]);
    bannersPersistence.findAll = findAllMock;

    bannersUsecase.loadBanners();

    expect(store.getState().banners).toStrictEqual(initialState.banners);
    expect(store.getState().adBannerType).toBe(AdBannerType.WITH_GRAPH);

    expect(findAllMock).toBeCalledTimes(1);
});

test('Test loadBanners function with saved adsBanner and covidBanner', () => {
    const store = createInitialTestState({ rateSearchKey: 'searchKey' });
    const injections = createUsecaseInjections(store);
    const bannersUsecase = new BannersUsecase(...injections);

    const [, { bannersPersistence }] = injections;

    const findAllMock = jest.fn<PersistedBannerConfig[], [string]>().mockReturnValue([
        { rateSearchKey: 'searchKey', banners: 'adsBanner' },
        { rateSearchKey: 'searchKey', banners: 'shortCarSupply' },
    ]);
    bannersPersistence.findAll = findAllMock;

    bannersUsecase.loadBanners();

    expect(store.getState().banners).toStrictEqual({
        ...initialState.banners,
        adsBanner: false,
        shortCarSupply: false,
    });
    expect(store.getState().adBannerType).toBe(AdBannerType.WITHOUT_GRAPH);

    expect(findAllMock).toBeCalledTimes(1);
});
