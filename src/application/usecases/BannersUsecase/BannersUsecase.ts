import { AdBannerType, Banners, BannersTypes } from '../../../domain/AdBanner';
import { FECError, FECErrorType } from '../../../domain/FECError';

import { Mutable } from '../../../utils/TypeUtils';

import { Usecase } from '../Usecase';

export class BannersUsecase extends Usecase {
    private getBanners = (banners: Banners, rateSearchKey: string): Banners => {
        const newBanners: Partial<Mutable<Banners>> = {};

        try {
            this.bannersPersistence.findAll(rateSearchKey).forEach((item) => {
                newBanners[item.banners] = false;
            });
        } catch (error) {
            this.loggerService.error(
                new FECError(
                    FECErrorType.UNEXPECTED_PERSISTED_DATA,
                    'Could not load banner persisted configuration',
                    error as Record<string, unknown>,
                ),
            );
        }

        return { ...banners, ...newBanners };
    };

    loadBanners(): void {
        const { banners, rateSearchKey } = this.getState();

        if (rateSearchKey) {
            this.setState({
                banners: this.getBanners(banners, rateSearchKey),

                /**
                 * The first char of the string will be converted to its ascii int code
                 * If even -> without graph
                 * If odd -> with graph
                 */
                adBannerType: rateSearchKey.charCodeAt(0) % 2 ? AdBannerType.WITHOUT_GRAPH : AdBannerType.WITH_GRAPH,
            });
        }
    }

    closeBanner(bannerType: BannersTypes): void {
        const { banners, rateSearchKey } = this.getState();

        if (!rateSearchKey) {
            return;
        }

        this.bannersPersistence.persist({ rateSearchKey, banners: bannerType });
        this.setState({ banners: { ...banners, [bannerType]: false } });
    }
}
