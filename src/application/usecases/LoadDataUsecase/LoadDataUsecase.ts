import { hasError, LoadingStatus } from '../../../domain/LoadingStatus';
import { AppState, hasFinished } from '../../../domain/AppState';
import { SearchParameters } from '../../../domain/SearchParameters';
import { hasSelectedFilters } from '../../../domain/Filter';
import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';

import { RatesFetchArgs } from '../../services';

import { Usecase } from '../Usecase';
import { INITIAL_PAGE } from '../../InitialState';
import URLUsecase from '../URLUsecase';

type LoadingSource =
    | LoadingStatus.LOADING_AUTOMATICALLY
    | LoadingStatus.LOADING_FOR_MAP
    | LoadingStatus.LOADING_BY_USER;
type DataControlArgs = {
    readonly loadingSource: LoadingSource;
    readonly onlyLoadOffersIfFinished?: boolean;
    readonly loadedTimes?: number;
};
type Args = Pick<RatesFetchArgs, 'rateSearchKey' | 'currentPage'> & DataControlArgs;

export class LoadDataUsecase extends Usecase {
    protected dispatchLoading = (loadingSource?: LoadingSource): string => {
        const requestId = new Date().toString();

        this.setState({
            error: null,
            loadingRV: loadingSource || LoadingStatus.LOADING_BY_USER,
            requestId,
        });

        return requestId;
    };

    protected dispatchSearchMetadata = async (
        rateSearchKey: string,
        requestId: AppState['requestId'],
    ): Promise<SearchParameters | null> => {
        const [configuration, search] = await Promise.all([
            this.backend.fetchConfiguration(rateSearchKey),
            this.backend.fetchSearch(rateSearchKey),
        ]);

        const { requestId: currentRequestId, dynamicConfiguration } = this.getState();
        const newConfig = { ...(dynamicConfiguration || {}), ...configuration };

        if (requestId === currentRequestId) {
            this.setState({
                /**
                 * Only loading the configuration
                 * even if we don't have offers data
                 */
                dynamicConfiguration: newConfig,
                search,
            });
            this.dataLoaded();

            return search;
        }

        return null;
    };

    protected resolveSelection = (
        { forceSelection, loadedTimes, filters }: Pick<AppState, 'loadedTimes' | 'forceSelection' | 'filters'>,
        search: SearchParameters,
    ): Pick<AppState, 'forceSelection'> => {
        if (
            /**
             * It is NOT first load
             */
            loadedTimes !== 0 ||
            /**
             * User already has a custom url selection
             */
            (forceSelection !== null && forceSelection.size > 0) ||
            /**
             * There are filters selected
             */
            hasSelectedFilters(filters)
        ) {
            /**
             * It is NOT first load
             */
            return { forceSelection };
        }

        /**
         * Enable automatically selected filters
         */
        const defaultForceSelection = new Map<string, string[]>();

        // enable good_supplier option
        defaultForceSelection.set('additionals', ['12']);

        // enable airport
        if (search.pickUp.suggestion.type === 'airport' && search.pickUp.suggestion.iata) {
            defaultForceSelection.set('pickupIatas', [search.pickUp.suggestion.iata]);
        }

        return {
            forceSelection: defaultForceSelection,
        };
    };

    protected scheduleAutomaticReload = (
        timeout: number,
        loadingSource: LoadingSource,
        onlyLoadOffersIfFinished: boolean,
        loadedTimes: number,
    ): void => {
        const { rateSearchKey: oldRateSearchKey } = this.getState();

        setTimeout(() => {
            const { rateSearchKey, currentPage, loadingRV } = this.getState();

            if (!rateSearchKey || oldRateSearchKey !== rateSearchKey || hasError(loadingRV)) {
                /**
                 * The search cannot be made if
                 * - We don't have a rateSearchKey
                 * - Another search was made in between
                 * - The data load is not succesfull/expired
                 */
                return;
            }

            void this.loadDataAction({
                rateSearchKey,
                currentPage,
                loadingSource,
                onlyLoadOffersIfFinished,
                loadedTimes,
            });
        }, timeout);
    };

    async loadDataAction(args: Args): Promise<void> {
        const requestId = this.dispatchLoading(args.loadingSource);

        try {
            const search = await this.dispatchSearchMetadata(args.rateSearchKey, requestId);

            if (search === null) {
                this.setState({ loadingRV: LoadingStatus.LOADED_SUCCESS });
                return;
            }

            const { rateSearchKey, currentPage, onlyLoadOffersIfFinished } = args;
            const { filters, packages, loadMapData, stations } = this.getState();
            const { forceSelection } = this.resolveSelection(this.getState(), search);

            const response = await this.backend.fetchRates({
                rateSearchKey,
                currentPage,
                filters,
                packages,
                forceSelection,
                loadMapData,
                stations,
                isOneWay: search.isOneWay,
            });
            const { requestId: currentRequestId, loadedTimes, configuration, showLoader, createdAt } = this.getState();

            if (
                // This request was not the last request, giving up
                requestId !== currentRequestId ||
                // Do not load rates if they are not finished loading
                (onlyLoadOffersIfFinished && !hasFinished({ ...response, configuration }))
            ) {
                this.setState({ loadingRV: LoadingStatus.LOADED_SUCCESS });
                return;
            }

            const loadedTime = args.loadedTimes || loadedTimes;

            this.setState({
                ...response,

                // If the response has been loaded, it means we can stop forcing selection there was one
                forceSelection: (hasFinished({ ...response, configuration }) ? null : forceSelection) || null,

                // Parameters/Filters
                rateSearchKey,

                // If the new response has valid total pages
                // and the pageCount has decreased, this will handle it
                currentPage: Math.min(response.totalPages || INITIAL_PAGE, currentPage),

                /**
                 * Other control variables
                 */
                loadingRV: LoadingStatus.LOADED_SUCCESS,
                loadedTimes: loadedTime,
                showLoader:
                    response.loadedPercentage !== 1 &&
                    (createdAt ? Date.now() - createdAt.getTime() < configuration.loadingBannerTimespan : showLoader),
            });

            this.dataLoaded();
        } catch (e) {
            const error = wrapError(e, FECErrorType.UNEXPECTED_DATA_LOAD);

            this.setState({
                loadingRV: LoadingStatus.LOADED_ERROR,
                error,
            });
            this.loggerService.error(error);
        }
    }

    dataLoaded = (): void => {
        const { loadedTimes, search } = this.getState();

        if (loadedTimes === 0) {
            this.analytics.onFirstDataLoadEvent(search);
        }

        const urlUsecase = this.getUsecase(URLUsecase);
        urlUsecase.offLoadToUrl();
    };

    requestData = (newSearch = false, loadingSource: LoadingSource = LoadingStatus.LOADING_BY_USER): void => {
        const {
            rateSearchKey,
            currentPage,
            configuration: { loadMoments },
        } = this.getState();

        if (!rateSearchKey) {
            /**
             * Unexpected scenario, the url was not properly formed, so it is not usable
             */
            this.loggerService.error(
                new FECError(
                    FECErrorType.UNEXPECTED_STATE,
                    'Request data function was executed without proper parameters',
                    {
                        rateSearchKey,
                    },
                ),
            );
            return;
        }

        if (newSearch) {
            /**
             * We need to schedule the actual searches
             *
             */
            loadMoments.forEach((moment, index) =>
                this.scheduleAutomaticReload(
                    moment,
                    moment === 0 ? LoadingStatus.LOADING_BY_USER : LoadingStatus.LOADING_AUTOMATICALLY,
                    moment === 0,
                    index,
                ),
            );
        } else {
            /**
             * If it is the same, we can do a search right away
             */
            void this.loadDataAction({
                rateSearchKey,
                currentPage,
                loadingSource,
            });
        }
    };

    hideLoader = (): void => {
        this.setState({ showLoader: false });
    };
}
