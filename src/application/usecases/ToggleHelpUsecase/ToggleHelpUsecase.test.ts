import ToggleHelpUsecase from '.';

import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';

test('Test invoke function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const toggleHelpUsecase = new ToggleHelpUsecase(...injections);

    expect(store.getState().showHelp).toBe(false);

    toggleHelpUsecase.invoke(true);
    expect(store.getState().showHelp).toBe(true);

    toggleHelpUsecase.invoke(false);
    expect(store.getState().showHelp).toBe(false);
});
