import { OfferDetailsTab } from '../../../domain/OfferDetailsTab';

import { Offer } from '../../../domain/Offer';

import { Usecase } from '../Usecase';

export class OfferDetailsUsecase extends Usecase {
    openDetails(tab: OfferDetailsTab, offer: Offer): void {
        this.setState({ detailsTab: tab, offer });
        this.analytics.onTermsAndConditionsToggle(true);
        this.analytics.onTermsAndConditionsTabOpen(tab);
    }

    changeTab(tab: OfferDetailsTab): void {
        const { detailsTab } = this.getState();

        if (detailsTab !== tab) {
            this.setState({ detailsTab: tab });
            this.analytics.onTermsAndConditionsTabOpen(tab);
        }
    }

    close(): void {
        this.setState({ detailsTab: null, offer: null });
        this.analytics.onTermsAndConditionsToggle(false);
    }
}
