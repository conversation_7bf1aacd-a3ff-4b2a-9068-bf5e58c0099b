import OfferDetailsUsecase from '.';

import { OfferDetailsTab } from '../../../domain/OfferDetailsTab';

import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';
import { BASE_OFFER } from '../../../../test/mockAssets/baseOffer';

test('Test openDetails function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const offerDetailsUsecase = new OfferDetailsUsecase(...injections);

    const [, { analytics }] = injections;

    const onTermsAndConditionsToggleMock = jest.fn<void, [boolean]>();
    analytics.onTermsAndConditionsToggle = onTermsAndConditionsToggleMock;
    const onTermsAndConditionsTabOpenMock = jest.fn<void, [OfferDetailsTab]>();
    analytics.onTermsAndConditionsTabOpen = onTermsAndConditionsTabOpenMock;

    expect(store.getState().detailsTab).toBe(null);
    expect(store.getState().offer).toBe(null);

    offerDetailsUsecase.openDetails(OfferDetailsTab.GENERAL_RATINGS, BASE_OFFER);

    expect(store.getState().detailsTab).toBe(OfferDetailsTab.GENERAL_RATINGS);
    expect(store.getState().offer).toBe(BASE_OFFER);
    expect(onTermsAndConditionsToggleMock).toBeCalledWith(true);
    expect(onTermsAndConditionsToggleMock).toBeCalledTimes(1);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledWith(OfferDetailsTab.GENERAL_RATINGS);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledTimes(1);
});

test('Test changeTab function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const offerDetailsUsecase = new OfferDetailsUsecase(...injections);

    const [, { analytics }] = injections;

    const onTermsAndConditionsTabOpenMock = jest.fn<void, [OfferDetailsTab]>();
    analytics.onTermsAndConditionsTabOpen = onTermsAndConditionsTabOpenMock;

    expect(store.getState().detailsTab).toBe(null);
    expect(store.getState().offer).toBe(null);

    offerDetailsUsecase.changeTab(OfferDetailsTab.GENERAL_RATINGS);
    expect(store.getState().detailsTab).toBe(OfferDetailsTab.GENERAL_RATINGS);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledWith(OfferDetailsTab.GENERAL_RATINGS);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledTimes(1);

    //Try to call with same tab and expect that logic wasn't called
    offerDetailsUsecase.changeTab(OfferDetailsTab.GENERAL_RATINGS);
    expect(store.getState().detailsTab).toBe(OfferDetailsTab.GENERAL_RATINGS);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledWith(OfferDetailsTab.GENERAL_RATINGS);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledTimes(1);

    offerDetailsUsecase.changeTab(OfferDetailsTab.MAP);
    expect(store.getState().detailsTab).toBe(OfferDetailsTab.MAP);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledWith(OfferDetailsTab.MAP);
    expect(onTermsAndConditionsTabOpenMock).toBeCalledTimes(2);
});

test('Test close function', () => {
    const store = createInitialTestState({ detailsTab: OfferDetailsTab.GENERAL_RATINGS, offer: BASE_OFFER });
    const injections = createUsecaseInjections(store);
    const mapUsecase = new OfferDetailsUsecase(...injections);

    const [, { analytics }] = injections;

    const onTermsAndConditionsToggleMock = jest.fn<void, [boolean]>();
    analytics.onTermsAndConditionsToggle = onTermsAndConditionsToggleMock;

    expect(store.getState().detailsTab).toBe(OfferDetailsTab.GENERAL_RATINGS);
    expect(store.getState().offer).toBe(BASE_OFFER);

    mapUsecase.close();

    expect(store.getState().detailsTab).toBe(null);
    expect(store.getState().offer).toBe(null);
    expect(onTermsAndConditionsToggleMock).toBeCalledWith(false);
    expect(onTermsAndConditionsToggleMock).toBeCalledTimes(1);
});
