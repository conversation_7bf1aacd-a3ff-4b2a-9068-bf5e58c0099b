import { AutocompleteSuggestion } from '../../../domain/Autocomplete';
import { FECError, FECErrorType } from '../../../domain/FECError';
import { SearchParameters } from '../../../domain/SearchParameters';
import { LoadingStatus as RVLoadingStatus } from '../../../domain/LoadingStatus';

import LoadDataUsecase from '../LoadDataUsecase';
import SearchUsecase from '.';

import { createUsecaseInjections, createInitialTestState } from '../../../../test/TestUtils';

const SEARCH: SearchParameters = {
    age: { id: '1', ageRange: [26] },
    isOneWay: true,
    pickUp: {
        locationName: 'Location',
        time: new Date(),
        suggestion: {
            type: 'railway',
            summary: 'railway summary',
            name: 'railwayname',
            iata: 'IATA',
            additionalInfo: 'railway additionalInfo',
            rawAutoCompleteData: 'railway rawAutoCompleteData',
            lat: 1,
            lng: 1,
        },
    },
    dropOff: {
        locationName: 'Location',
        time: new Date(),
        suggestion: {
            type: 'railway',
            summary: 'railway summary',
            name: 'railwayname',
            iata: 'IATA',
            additionalInfo: 'railway additionalInfo',
            rawAutoCompleteData: 'railway rawAutoCompleteData',
            lat: 1,
            lng: 1,
        },
    },
};

test('Test loadAutocompleteData with empty searchedTerm', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const searchUsecase = new SearchUsecase(...injections);
    const [, { autocomplete, loggerService }] = injections;

    const fetchSuggestionsMock = jest.fn<Promise<AutocompleteSuggestion[]>, [string]>();
    autocomplete.fetchSuggestions = fetchSuggestionsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    await searchUsecase.loadAutocompleteData('');

    expect(store.getState().autoComplete).toStrictEqual({ searchedTerm: '', suggestions: [], error: null });

    expect(fetchSuggestionsMock).toBeCalledTimes(0);
    expect(loggerErrorMock).toBeCalledTimes(0);
});

test('Test loadAutocompleteData with error from fetchSuggestions', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const searchUsecase = new SearchUsecase(...injections);
    const [, { autocomplete, loggerService }] = injections;

    const error = new FECError(FECErrorType.UNEXPECTED_AUTOCOMPLETE_DATA_LOAD, 'Autocomplete error');

    const fetchSuggestionsMock = jest.fn<Promise<AutocompleteSuggestion[]>, [string]>(() => {
        throw error;
    });
    autocomplete.fetchSuggestions = fetchSuggestionsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    await searchUsecase.loadAutocompleteData('Term');

    expect(store.getState().autoComplete).toStrictEqual({ searchedTerm: 'Term', suggestions: [], error });

    expect(fetchSuggestionsMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test loadAutocompleteData with correct data', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const searchUsecase = new SearchUsecase(...injections);
    const [, { autocomplete, loggerService }] = injections;

    const suggestions: AutocompleteSuggestion[] = [
        {
            type: 'airport',
            summary: 'summary',
            name: 'name',
            iata: null,
            additionalInfo: 'additionalInfo',
            rawAutoCompleteData: 'rawAutoCompleteData',
            lat: 1,
            lng: 1,
        },
        {
            type: 'railway',
            summary: 'railway summary',
            name: 'railwayname',
            iata: 'IATA',
            additionalInfo: 'railway additionalInfo',
            rawAutoCompleteData: 'railway rawAutoCompleteData',
            lat: 1,
            lng: 1,
        },
    ];

    const fetchSuggestionsMock = jest
        .fn<Promise<AutocompleteSuggestion[]>, [string]>()
        .mockReturnValue(Promise.resolve(suggestions));
    autocomplete.fetchSuggestions = fetchSuggestionsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    await searchUsecase.loadAutocompleteData('Term');

    expect(store.getState().autoComplete).toStrictEqual({ searchedTerm: 'Term', suggestions, error: null });

    expect(fetchSuggestionsMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(0);
});

test('Test makeNewSearch with empty search parameter', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const searchUsecase = new SearchUsecase(...injections);
    const [, { backend, loggerService }] = injections;

    const startSearchMock = jest.fn<Promise<string>, [SearchParameters]>();
    backend.startSearch = startSearchMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    const error = new FECError(FECErrorType.UNEXPECTED_SEARCH_START, 'Search object not loaded');

    await searchUsecase.makeNewSearch();

    expect(store.getState().rateSearchKey).toBe(null);
    expect(store.getState().search).toBe(null);
    expect(store.getState().loadingRV).toBe(RVLoadingStatus.LOADED_ERROR);
    expect(store.getState().error).toStrictEqual(error);

    expect(startSearchMock).toBeCalledTimes(0);
    expect(requestDataMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test makeNewSearch with error from startSearch', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const searchUsecase = new SearchUsecase(...injections);
    const [, { backend, loggerService }] = injections;

    const error = new FECError(FECErrorType.UNEXPECTED_SEARCH_START, 'Search object not loaded');

    const startSearchMock = jest.fn<Promise<string>, [SearchParameters]>(() => {
        throw error;
    });
    backend.startSearch = startSearchMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    await searchUsecase.makeNewSearch(SEARCH);

    expect(store.getState().rateSearchKey).toBe(null);
    expect(store.getState().search).toBe(SEARCH);
    expect(store.getState().loadingRV).toBe(RVLoadingStatus.LOADED_ERROR);
    expect(store.getState().error).toStrictEqual(error);

    expect(startSearchMock).toBeCalledTimes(1);
    expect(requestDataMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test makeNewSearch with correct data', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const searchUsecase = new SearchUsecase(...injections);
    const [, { backend, loggerService }] = injections;

    const startSearchMock = jest.fn<Promise<string>, [SearchParameters]>().mockReturnValue(Promise.resolve('newKey'));
    backend.startSearch = startSearchMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    await searchUsecase.makeNewSearch(SEARCH);

    expect(store.getState().rateSearchKey).toBe('newKey');
    expect(store.getState().search).toBe(SEARCH);
    expect(store.getState().loadingRV).toBe(RVLoadingStatus.LOADING_BY_USER);
    expect(store.getState().error).toStrictEqual(null);

    expect(startSearchMock).toBeCalledTimes(1);
    expect(requestDataMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(0);
});
