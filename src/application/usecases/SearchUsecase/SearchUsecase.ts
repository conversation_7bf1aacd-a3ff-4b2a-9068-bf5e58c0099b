import { AutocompleteData } from '../../../domain/AppState';
import { AutocompleteSuggestion } from '../../../domain/Autocomplete';
import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';
import { LoadingStatus } from '../../../domain/LoadingStatus';
import { SearchParameters } from '../../../domain/SearchParameters';

import { initialState } from '../../InitialState';

import { Usecase } from '../Usecase';
import LoadDataUsecase from '../LoadDataUsecase';

export class SearchUsecase extends Usecase {
    async loadAutocompleteData(searchedTerm: string, showOnlyType?: AutocompleteSuggestion['type']): Promise<void> {
        let data: AutocompleteData;

        try {
            const rawSuggestions = searchedTerm ? await this.autocomplete.fetchSuggestions(searchedTerm) : [];
            const suggestions = rawSuggestions.filter(({ type }) => !showOnlyType || type === showOnlyType);

            data = { searchedTerm, suggestions, error: null };
        } catch (e) {
            const error = wrapError(e, FECErrorType.UNEXPECTED_AUTOCOMPLETE_DATA_LOAD);
            data = { searchedTerm, suggestions: [], error };

            this.loggerService.error(error);
        }

        this.setState({ autoComplete: data });
    }

    async makeNewSearch(newSearch?: SearchParameters): Promise<void> {
        const search = newSearch || this.getState().search;

        try {
            if (!search) {
                throw new FECError(FECErrorType.UNEXPECTED_STATE, 'Search object not loaded');
            }

            const rateSearchKey = await this.backend.startSearch(search);

            this.setState({
                ...initialState,
                search,
                rateSearchKey,
                loadingRV: LoadingStatus.LOADING_BY_USER,
                error: null,
            });
        } catch (e) {
            const error = wrapError(e, FECErrorType.UNEXPECTED_SEARCH_START);

            this.setState({
                search,
                rateSearchKey: null,
                loadingRV: LoadingStatus.LOADED_ERROR,
                error,
            });
            this.loggerService.error(error);
        }

        const loadDataUsecase = this.getUsecase(LoadDataUsecase);
        loadDataUsecase.requestData(true);
    }
}
