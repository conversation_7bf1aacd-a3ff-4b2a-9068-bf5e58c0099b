import { FECError } from '../../../domain/FECError';
import { FECBookingError, PSD2Response } from '../../../domain/Booking';

import { Usecase } from '../Usecase';
import CreateBookingUsecase from '../CreateBookingUsecase';

export class SecuredCreditCardUsecase extends Usecase {
    submitTransactionId(secureCardTransactionId: string): void {
        this.setState({ secureCardTransactionId });
        void this.getUsecase(CreateBookingUsecase).invoke();
    }

    submitPSD2Response(response: PSD2Response | FECBookingError): void {
        if (response instanceof FECError) {
            this.setState({ error: response });
            this.loggerService.error(response);
            this.analytics.onBookingError(response);
        } else {
            void this.getUsecase(CreateBookingUsecase).invoke(response);
        }
    }
}
