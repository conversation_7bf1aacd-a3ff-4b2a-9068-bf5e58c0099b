import { FECBookingError, BookingErrorType } from '../../../domain/Booking';

import CreateBookingUsecase from '../CreateBookingUsecase';
import SecuredCreditCardUsecase from '.';

import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';

test('Test submitTransactionId function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const createBookingUsecase = new CreateBookingUsecase(...injections);
    const createBookingInvokeMock = jest.fn<Promise<void>, []>();
    createBookingUsecase.invoke = createBookingInvokeMock;
    const securedCreditCardUsecase = new SecuredCreditCardUsecase(...injections);

    const secureCardTransactionId = '1';

    expect(store.getState().secureCardTransactionId).toBe(null);

    securedCreditCardUsecase.submitTransactionId(secureCardTransactionId);

    expect(store.getState().secureCardTransactionId).toBe(secureCardTransactionId);
    expect(createBookingInvokeMock).toBeCalledTimes(1);
});

test('Test submitPSD2Response function with error', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const createBookingUsecase = new CreateBookingUsecase(...injections);
    const createBookingInvokeMock = jest.fn<Promise<void>, []>();
    createBookingUsecase.invoke = createBookingInvokeMock;
    const securedCreditCardUsecase = new SecuredCreditCardUsecase(...injections);

    const [, { analytics, loggerService }] = injections;

    const onBookingErrorMock = jest.fn<void, []>();
    analytics.onBookingError = onBookingErrorMock;
    const loggerErrorMock = jest.fn<void, []>();
    loggerService.error = loggerErrorMock;

    const error = new FECBookingError(BookingErrorType.UNEXPECTED);
    securedCreditCardUsecase.submitPSD2Response(error);

    expect(store.getState().error).toStrictEqual(error);

    expect(onBookingErrorMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(1);
    expect(createBookingInvokeMock).toBeCalledTimes(0);
});

test('Test submitPSD2Response function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const createBookingUsecase = new CreateBookingUsecase(...injections);
    const createBookingInvokeMock = jest.fn<Promise<void>, []>();
    createBookingUsecase.invoke = createBookingInvokeMock;
    const securedCreditCardUsecase = new SecuredCreditCardUsecase(...injections);

    const [, { analytics, loggerService }] = injections;

    const onBookingErrorMock = jest.fn<void, []>();
    analytics.onBookingError = onBookingErrorMock;
    const loggerErrorMock = jest.fn<void, []>();
    loggerService.error = loggerErrorMock;

    const PSD2FingerprintResponse = { fingerprint: 'fingerprint', paymentData: 'paymentData' };
    securedCreditCardUsecase.submitPSD2Response(PSD2FingerprintResponse);

    expect(onBookingErrorMock).toBeCalledTimes(0);
    expect(loggerErrorMock).toBeCalledTimes(0);
    expect(createBookingInvokeMock).toBeCalledTimes(1);
});
