import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';
import {
    FECBookingError,
    BookingErrorType,
    BookingRequest,
    isSuccessfulBookingResponse,
    PSD2Response,
} from '../../../domain/Booking';
import { CreditCard, CreditCardStatus, Rate } from '../../../domain/Rate';
import { BookingStatus } from '../../../domain/AppState';

import { Usecase } from '../Usecase';

export class CreateBookingUsecase extends Usecase {
    private createCreditCardBookingRequest(
        creditCard: CreditCard,
        psd2Response: PSD2Response | null,
    ): BookingRequest['creditCard'] {
        const {
            userData: {
                creditCardExpireDate: { value: expiryDate },
                creditCardNumber: { value: creditCardNumber },
                creditCardSecurityCode: { value: creditCardSecurityCode },
            },
            secureCardTransactionId,
        } = this.getState();

        // TODO: to remove. the flow is currently not used.
        if (creditCard.status === CreditCardStatus.PROVIDER_HANDLED) {
            /**
             * We don't need to try to use or pass credit card data to the backend
             */
            return null;
        }

        if (!expiryDate) {
            /**
             * Expiry date should be here from this moment forward
             */
            throw new FECBookingError(BookingErrorType.INVALID_CREDIT_CARD_DATA);
        }

        if (creditCard.requiresPCICompliance) {
            if (secureCardTransactionId) {
                /**
                 * All good, send data with transaction id
                 */
                return { expiryDate, secureCardTransactionId, psd2Response };
            }

            /**
             * There should be a transaction id here
             */
            throw new FECBookingError(BookingErrorType.INVALID_CREDIT_CARD_DATA);
        }

        if (!creditCardNumber || !creditCardSecurityCode) {
            /**
             * Security code or cc number should be here
             */
            throw new FECBookingError(BookingErrorType.INVALID_CREDIT_CARD_DATA);
        }

        return { number: creditCardNumber, securityCode: creditCardSecurityCode, expiryDate, psd2Response };
    }

    private createBookingRequest({ id, creditCard }: Rate, psd2Response: PSD2Response | null): BookingRequest {
        const { userData, userSelection } = this.getState();

        if (!userData.country.value) {
            /**
             * Country is required
             */
            throw new FECBookingError(BookingErrorType.INVALID_COUNTRY);
        }

        if (!userData.birthDate.value) {
            /**
             * Birth date is required
             */
            throw new FECBookingError(BookingErrorType.INVALID_BIRTH_DATE);
        }

        return {
            rate: id,
            gender: userData.salutation.value,
            firstName: userData.firstName.value,
            lastName: userData.lastName.value,
            company: userData.company.value,

            address: userData.address.value,
            zipCode: userData.zipCode.value,
            city: userData.city.value,
            country: userData.country.value,

            email: userData.email.value,
            phoneNumber: userData.phoneNumber.value,
            birthDate: userData.birthDate.value,
            comments: userData.comments.value,
            flightNumber: userData.flightNumber.value,

            extras: userSelection.extras,
            selectedInsurance: userSelection.selectedInsurance,

            creditCard: this.createCreditCardBookingRequest(creditCard, psd2Response),
        };
    }

    async invoke(psd2Response: PSD2Response | null = null): Promise<void> {
        const { bookingStatus, rate, priceSummary, userData, userSelection, optionalInsurance, sourceLandingPage } =
            this.getState();

        /**
         * Check if booking is already in progress
         */
        if (bookingStatus === BookingStatus.LOADING) {
            return;
        }

        try {
            if (!rate || !priceSummary) {
                throw new FECError(FECErrorType.UNEXPECTED_STATE, 'Rate or price summary not available');
            }

            const request = this.createBookingRequest(rate, psd2Response);

            this.setState({ bookingStatus: BookingStatus.LOADING });
            this.analytics.onBeforeRateBooking();

            const bookingResponse = await this.backend.bookRate(request);

            this.setState({ bookingResponse });
            if (isSuccessfulBookingResponse(bookingResponse)) {
                this.setState({ bookingStatus: BookingStatus.SUCCESS });
                this.analytics.onBookingSuccess(
                    { rate, priceSummary, userData, userSelection, sourceLandingPage },
                    optionalInsurance,
                    bookingResponse,
                );
            } else {
                this.setState({ bookingStatus: BookingStatus.CHALLENGED });
                this.analytics.onBookingChallenged();
            }
        } catch (e) {
            const error = wrapError(e, FECErrorType.BOOKING_ERROR);
            this.setState({ bookingStatus: BookingStatus.ERROR, error });
            this.loggerService.error(error);
            this.analytics.onBookingError(error);
        }
    }
}
