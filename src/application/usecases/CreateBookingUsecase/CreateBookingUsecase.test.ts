import { FECError, FECErrorType } from '../../../domain/FECError';
import { FECBookingError, BookingErrorType, BookingRequest, BookingResponse } from '../../../domain/Booking';
import { ExtraType } from '../../../domain/Extra';
import { CreditCardStatus } from '../../../domain/Rate';
import { BookingStatus } from '../../../domain/AppState';

import { createInitialUserField, initialState } from '../../InitialState';

import CreateBookingUsecase from '.';

import {
    mockedBookingResponse,
    mockedRates,
    mockedChallengedBookingResponse,
} from '../../../../test/services/MockedBackend';
import { createInitialTestState, createUsecaseInjections } from '../../../../test/TestUtils';

describe(CreateBookingUsecase.name, () => {
    it('fails when Rate not available', async () => {
        const store = createInitialTestState();
        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, loggerService }] = injections;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        const error = new FECError(FECErrorType.BOOKING_ERROR, 'Rate or price summary not available');
        expect(store.getState().error).toStrictEqual(error);
        expect(store.getState().bookingStatus).toBe(BookingStatus.ERROR);

        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(error);
    });

    it('fails when price summary not available', async () => {
        const store = createInitialTestState({ rate: mockedRates.BASIC_FIRST.rate });
        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, loggerService }] = injections;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        const error = new FECError(FECErrorType.BOOKING_ERROR, 'Rate or price summary not available');
        expect(store.getState().error).toStrictEqual(error);
        expect(store.getState().bookingStatus).toBe(BookingStatus.ERROR);

        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(error);
    });

    it('fails when there is no country selected', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [
                    {
                        type: ExtraType.ADDITIONAL_DRIVER,
                        referenceId: '1',
                        comment: '',
                        quantity: 1,
                        includedQuantity: 0,
                        price: null,
                    },
                ],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: null }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_COUNTRY);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('fails when there is given birthdate', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [
                    {
                        type: ExtraType.ADDITIONAL_DRIVER,
                        referenceId: '1',
                        comment: '',
                        quantity: 1,
                        includedQuantity: 0,
                        price: null,
                    },
                ],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: null }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_BIRTH_DATE);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    // TODO: remove, the flow is currently not used
    it('suceeds without passing any payment data when provider handles payment data', async () => {
        const birthDate = new Date();
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            rate: {
                ...mockedRates.BASIC_FIRST.rate,
                creditCard: {
                    status: CreditCardStatus.PROVIDER_HANDLED,
                },
            },
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: birthDate }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedBookingResponse));
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toBeNull();

        expect(bookRateMock).lastCalledWith({
            rate: 'BASIC_FIRST',
            gender: 'MALE',
            firstName: '',
            lastName: '',
            company: null,
            address: '',
            zipCode: '',
            city: '',
            country: { countryCode: 'BR', name: 'Brasil' },
            email: '',
            phoneNumber: '',
            birthDate,
            comments: null,
            flightNumber: '',
            extras: [],
            selectedInsurance: { areTermsAccepted: false },
            creditCard: null,
        });
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(1);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });

    it('suceeds when pci compliance is required and passed a transaction id', async () => {
        const birthDate = new Date();
        const expiryDate = new Date();
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            rate: {
                ...mockedRates.BASIC_FIRST.rate,
                creditCard: {
                    status: CreditCardStatus.LEGALLY_REQUIRED,
                    requiresPCICompliance: true,
                    paymentMethods: [],
                },
            },
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: birthDate }),
                creditCardExpireDate: createInitialUserField({ value: expiryDate }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
            secureCardTransactionId: '123',
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedBookingResponse));
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toBeNull();

        expect(bookRateMock).lastCalledWith({
            rate: 'BASIC_FIRST',
            gender: 'MALE',
            firstName: '',
            lastName: '',
            company: null,
            address: '',
            zipCode: '',
            city: '',
            country: { countryCode: 'BR', name: 'Brasil' },
            email: '',
            phoneNumber: '',
            birthDate,
            comments: null,
            flightNumber: '',
            extras: [],
            selectedInsurance: { areTermsAccepted: false },
            creditCard: {
                expiryDate,
                psd2Response: null,
                secureCardTransactionId: '123',
            },
        });
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(1);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });

    it('suceeds if no comments are passed in an extra that does not require it', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            rate: {
                ...mockedRates.BASIC_FIRST.rate,
                creditCard: {
                    status: CreditCardStatus.PROVIDER_HANDLED,
                },
            },
            userSelection: {
                extras: [
                    {
                        type: ExtraType.BABY_SEAT,
                        referenceId: '1',
                        comment: null,
                        quantity: 1,
                        includedQuantity: 0,
                        price: null,
                    },
                ],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);
        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedBookingResponse));
        backend.bookRate = bookRateMock;
        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toBeNull();
        expect(bookRateMock).toBeCalledTimes(1);
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(1);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });

    it('fails when expiration date is not given', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: null }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_CREDIT_CARD_DATA);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('fails when pci compliance is required but no transaction id is stored', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            rate: {
                ...mockedRates.BASIC_FIRST.rate,
                creditCard: {
                    status: CreditCardStatus.LEGALLY_REQUIRED,
                    paymentMethods: [],
                    requiresPCICompliance: true,
                },
            },
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
            secureCardTransactionId: null,
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_CREDIT_CARD_DATA);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('fails when no credit card number is given', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: null }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_BIRTH_DATE);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('fails when no credit card security code is given', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: null }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.INVALID_BIRTH_DATE);
        const bookRateMock = jest.fn();
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(0);
        expect(onBeforeRateBookingMock).toBeCalledTimes(0);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('fails with fail response from bookRate', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userSelection: {
                extras: [],
                selectedInsurance: { areTermsAccepted: false },
            },
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });

        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookingError = new FECBookingError(BookingErrorType.UNAUTHORIZED);
        const bookRateMock = jest.fn<Promise<BookingResponse>, [BookingRequest]>().mockRejectedValue(bookingError);
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, [FECError]>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toStrictEqual(bookingError);

        expect(bookRateMock).toBeCalledTimes(1);
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(1);
        expect(loggerErrorMock).toBeCalledWith(bookingError);
    });

    it('succeeds with SuccessfulBookingResponse from bookRate', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });
        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedBookingResponse));
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, []>();
        loggerService.error = loggerErrorMock;

        await createBookingUsecase.invoke();

        expect(store.getState().error).toBe(null);
        expect(store.getState().bookingResponse).toStrictEqual(mockedBookingResponse);

        expect(bookRateMock).toBeCalledTimes(1);
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(1);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });

    it('succeeds ChallengedBookingResponse from bookRate', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });
        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedChallengedBookingResponse));
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, []>();
        loggerService.error = loggerErrorMock;

        const PSD2FingerprintResponse = { fingerprint: 'fingerprint', paymentData: 'paymentData' };
        await createBookingUsecase.invoke(PSD2FingerprintResponse);

        expect(store.getState().error).toBe(null);
        expect(store.getState().bookingResponse).toStrictEqual(mockedChallengedBookingResponse);

        expect(bookRateMock).toBeCalledTimes(1);
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(0);
        expect(onBookingChallengedMock).toBeCalledTimes(1);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });

    it('does not execute 2 calls when 2 invocations are done at the same time', async () => {
        const store = createInitialTestState({
            ...mockedRates.BASIC_FIRST,
            userData: {
                ...initialState.userData,
                country: createInitialUserField({ value: { countryCode: 'BR', name: 'Brasil' } }),
                birthDate: createInitialUserField({ value: new Date() }),
                creditCardExpireDate: createInitialUserField({ value: new Date() }),
                creditCardNumber: createInitialUserField({ value: '****************' }),
                creditCardSecurityCode: createInitialUserField({ value: '737' }),
            },
        });
        const injections = createUsecaseInjections(store);
        const createBookingUsecase = new CreateBookingUsecase(...injections);

        const [, { analytics, backend, loggerService }] = injections;

        const bookRateMock = jest
            .fn<Promise<BookingResponse>, [BookingRequest]>()
            .mockReturnValue(Promise.resolve(mockedBookingResponse));
        backend.bookRate = bookRateMock;

        const onBeforeRateBookingMock = jest.fn<void, []>();
        analytics.onBeforeRateBooking = onBeforeRateBookingMock;
        const onBookingSuccessMock = jest.fn<void, []>();
        analytics.onBookingSuccess = onBookingSuccessMock;
        const onBookingChallengedMock = jest.fn<void, []>();
        analytics.onBookingChallenged = onBookingChallengedMock;
        const onBookingErrorMock = jest.fn<void, []>();
        analytics.onBookingError = onBookingErrorMock;
        const loggerErrorMock = jest.fn<void, []>();
        loggerService.error = loggerErrorMock;

        await Promise.all([createBookingUsecase.invoke(), createBookingUsecase.invoke()]);

        expect(store.getState().error).toBe(null);
        expect(store.getState().bookingResponse).toStrictEqual(mockedBookingResponse);

        expect(bookRateMock).toBeCalledTimes(1);
        expect(onBeforeRateBookingMock).toBeCalledTimes(1);
        expect(onBookingSuccessMock).toBeCalledTimes(1);
        expect(onBookingChallengedMock).toBeCalledTimes(0);
        expect(onBookingErrorMock).toBeCalledTimes(0);
        expect(loggerErrorMock).toBeCalledTimes(0);
    });
});
