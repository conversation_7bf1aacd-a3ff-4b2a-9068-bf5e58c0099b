import { FECError, FECErrorType, wrapError } from '../../../domain/FECError';

import { Usecase } from '../Usecase';

export class TermsUsecase extends Usecase {
    async loadTerms(rateId?: string): Promise<void> {
        let response: string | null;
        const { rateSearchKey, terms: allTerms } = this.getState();

        if (!rateSearchKey || !rateId) {
            const error = new FECError(FECErrorType.UNEXPECTED_STATE, 'RateSearchKey should be set');
            this.loggerService.error(error);
            throw error;
        }
        try {
            response = allTerms[rateId];
            response = response == undefined ? await this.backend.loadTerms({ rateSearchKey, rateId }) : response;
        } catch (e) {
            this.loggerService.error(wrapError(e, FECErrorType.UNEXPECTED_TERMS_DATA_LOAD));
            response = null;
        }

        const { terms } = this.getState();

        this.setState({
            terms: {
                ...terms,
                // Rate ids should be unique
                [rateId]: response,
            },
        });
    }

    async loadProviderTerms(termsLink?: string, providerId?: number): Promise<void> {
        let response: string | null;
        const { providerTerms: terms } = this.getState();

        if (!providerId || !termsLink) {
            const error = new FECError(FECErrorType.UNEXPECTED_STATE, 'providerId and termsLink should be set');
            this.loggerService.error(error);
            throw error;
        }

        try {
            response = terms[providerId];
            response = response === undefined ? await this.backend.loadTerms({ termsLink }) : response;
        } catch (e) {
            response = null;
            this.loggerService.error(wrapError(e, FECErrorType.UNEXPECTED_TERMS_DATA_LOAD));
        }

        const { providerTerms } = this.getState();

        this.setState({
            providerTerms: {
                ...providerTerms,
                [providerId]: response,
            },
        });
    }
}
