import TermsUsecase from '.';

import { FECError, FECErrorType } from '../../../domain/FECError';

import { LoadTermsArgs } from '../../services/Backend';

import { createUsecaseInjections, createInitialTestState, delay } from '../../../../test/TestUtils';

test('Test loadTerms function without rateSearchKey', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);
    const [, { backend, loggerService }] = injections;

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>();
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().terms).toStrictEqual({});

    try {
        await termsUsecase.loadTerms('1');
        fail('loadTerms must throw error ');
    } catch (e) {
        expect(e).toStrictEqual(new FECError(FECErrorType.UNEXPECTED_STATE, 'RateSearchKey should be set'));
    }

    expect(loadTermsMock).toBeCalledTimes(0);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test loadTerms function with error from backend.loadTerms', async () => {
    const store = createInitialTestState({ rateSearchKey: 'rateSearchKey' });
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);

    const [, { backend, loggerService }] = injections;

    const error = new FECError(FECErrorType.UNEXPECTED_STATE, 'Backend error');

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>(() => {
        throw error;
    });
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().terms).toStrictEqual({});

    await termsUsecase.loadTerms('1');

    expect(store.getState().terms).toStrictEqual({ '1': null });

    expect(loadTermsMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test loadTerms function with valid terms from backend.loadTerms', async () => {
    const store = createInitialTestState({ rateSearchKey: 'rateSearchKey' });
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);

    const [, { backend, loggerService }] = injections;

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>().mockReturnValue(Promise.resolve('Terms'));
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().terms).toStrictEqual({});

    await termsUsecase.loadTerms('1');

    expect(store.getState().terms).toStrictEqual({ '1': 'Terms' });

    expect(loadTermsMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(0);
});

test('Test 2 calls of loadTerms function', async () => {
    const store = createInitialTestState({ rateSearchKey: 'rateSearchKey' });
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);

    const [, { backend, loggerService }] = injections;

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>(async ({ rateSearchKey, rateId }) => {
        await delay(10);
        return `${rateId} Terms ${rateSearchKey}`;
    });
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().terms).toStrictEqual({});

    await Promise.all([termsUsecase.loadTerms('1'), termsUsecase.loadTerms('2')]);

    expect(store.getState().terms).toStrictEqual({ '1': '1 Terms rateSearchKey', '2': '2 Terms rateSearchKey' });

    expect(loadTermsMock).toBeCalledTimes(2);
    expect(loggerErrorMock).toBeCalledTimes(0);
});

test('Test loadProviderTerms function without termsLink', async () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);
    const [, { backend, loggerService }] = injections;

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>();
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().providerTerms).toStrictEqual({});

    try {
        await termsUsecase.loadProviderTerms();
        fail('loadTerms must throw error ');
    } catch (e) {
        expect(e).toStrictEqual(new FECError(FECErrorType.UNEXPECTED_STATE, 'providerId and termsLink should be set'));
    }

    expect(loadTermsMock).toBeCalledTimes(0);
    expect(loggerErrorMock).toBeCalledTimes(1);
});

test('Test loadProviderTerms function with valid terms from backend.loadTerms', async () => {
    const store = createInitialTestState({ rateSearchKey: 'rateSearchKey' });
    const injections = createUsecaseInjections(store);
    const termsUsecase = new TermsUsecase(...injections);

    const [, { backend, loggerService }] = injections;

    const loadTermsMock = jest.fn<Promise<string>, [LoadTermsArgs]>().mockReturnValue(Promise.resolve('Terms'));
    backend.loadTerms = loadTermsMock;
    const loggerErrorMock = jest.fn<void, [FECError]>();
    loggerService.error = loggerErrorMock;

    expect(store.getState().providerTerms).toStrictEqual({});

    await termsUsecase.loadProviderTerms('link', 1);

    expect(store.getState().providerTerms).toStrictEqual({ '1': 'Terms' });

    expect(loadTermsMock).toBeCalledTimes(1);
    expect(loggerErrorMock).toBeCalledTimes(0);
});
