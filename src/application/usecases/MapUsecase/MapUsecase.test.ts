import LoadDataUsecase from '../LoadDataUsecase';
import MapUsecase from '.';

import { createUsecaseInjections, createInitialTestState } from '../../../../test/TestUtils';

test('Test toggleMap function', () => {
    const store = createInitialTestState();
    const injections = createUsecaseInjections(store);
    const loadDataUsecase = new LoadDataUsecase(...injections);
    const requestDataMock = jest.fn<void, []>();
    loadDataUsecase.requestData = requestDataMock;
    const mapUsecase = new MapUsecase(...injections);

    expect(store.getState().isMapOpen).toBe(null);
    expect(store.getState().loadMapData).toBe(false);

    mapUsecase.toggleMap(true);
    expect(store.getState().isMapOpen).toBe(true);
    expect(store.getState().loadMapData).toBe(true);
    expect(requestDataMock).toBeCalledTimes(1);

    mapUsecase.toggleMap(false);
    expect(store.getState().isMapOpen).toBe(false);
    expect(store.getState().loadMapData).toBe(false);
    expect(requestDataMock).toBeCalledTimes(2);
});
