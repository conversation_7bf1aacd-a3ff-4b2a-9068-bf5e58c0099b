import { LoadingStatus as RVLoadingStatus } from '../../../domain/LoadingStatus';

import { Usecase } from '../Usecase';
import LoadDataUsecase from '../LoadDataUsecase';

export class MapUsecase extends Usecase {
    toggleMap(isOpen: boolean): void {
        this.setState({ isMapOpen: isOpen, loadMapData: isOpen });

        const loadDataUsecase = this.getUsecase(LoadDataUsecase);
        loadDataUsecase.requestData(false, RVLoadingStatus.LOADING_FOR_MAP);
    }
}
